import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/sizes.dart';

/// 对话框主题设置
class AppDialogTheme {
  /// 基础对话框的通用样式 - 亮色模式
  static DialogThemeData lightDialogTheme() {
    return DialogThemeData(
      backgroundColor: AppColors.background100,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
        side: BorderSide(color: AppColors.border200, width: 1),
      ),
      alignment: Alignment.center,
    );
  }

  /// 基础对话框的通用样式 - 暗色模式
  static DialogThemeData darkDialogTheme() {
    return DialogThemeData(
      backgroundColor: AppColors.backgroundDark200,
      elevation: 8,
      shadowColor: Colors.black.withValues(alpha: 0.5),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
        side: BorderSide(color: AppColors.borderDark200, width: 1),
      ),
      alignment: Alignment.center,
    );
  }
}
