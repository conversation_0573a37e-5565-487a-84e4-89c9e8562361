import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 浮层位置枚举
enum PopupPosition { bottom, top, right, left }

/// 弹出浮层组件
///
/// 提供一个通用的浮层弹出控件，可用于创建下拉菜单、上下文菜单等需要在特定组件附近显示浮层的场景。
/// 支持通过[position]属性控制浮层位置，通过[controller]控制浮层显示/隐藏。
/// 也可通过静态方法[showAtPosition]在指定位置显示浮层。
class PopupOverlay extends StatefulWidget {
  /// 触发浮层显示的子组件
  final Widget child;

  final GlobalKey<NavigatorState> navigatorKey;

  /// 构建浮层内容的builder函数
  final Widget Function(BuildContext context, VoidCallback closeOverlay)
  builder;

  /// 浮层的尺寸约束
  final BoxConstraints? constraints;

  /// 浮层与触发组件的偏移量
  final Offset offset;

  /// 是否点击外部关闭
  final bool closeOnTapOutside;

  /// 浮层位置
  final PopupPosition position;

  /// 浮层背景颜色
  final Color? backgroundColor;

  /// 浮层边框颜色
  final Color? borderColor;

  /// 浮层边框宽度
  final double borderWidth;

  /// 浮层阴影
  final double elevation;

  /// 浮层圆角
  final BorderRadius borderRadius;

  /// 弹出动画时长
  final Duration animationDuration;

  /// 控制器，用于在外部控制浮层显示/隐藏
  final PopupOverlayController? controller;

  /// 浮层关闭时的回调函数
  final VoidCallback? onClose;

  const PopupOverlay({
    super.key,
    required this.navigatorKey,
    required this.child,
    required this.builder,
    this.constraints,
    this.offset = Offset.zero,
    this.closeOnTapOutside = true,
    this.position = PopupPosition.bottom,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.elevation = 8,
    this.borderRadius = const BorderRadius.all(
      Radius.circular(AppRadiusSize.radius6),
    ),
    this.animationDuration = const Duration(milliseconds: 300),
    this.controller,
    this.onClose,
  });

  /// 在指定位置显示浮层
  ///
  /// 提供一种不需要锚定组件的浮层展示方式，直接在指定坐标位置显示浮层。
  static OverlayEntry? showAtPosition({
    required BuildContext context,
    required Widget Function(BuildContext context, VoidCallback close) builder,
    required Offset position,
    BoxConstraints? constraints,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 1.0,
    double elevation = 8,
    BorderRadius? borderRadius,
    Duration animationDuration = const Duration(milliseconds: 150),
  }) {
    final OverlayState? overlay = Navigator.of(context).overlay;
    if (overlay == null) return null;

    late OverlayEntry entry;
    void close() => entry.remove();

    // 应用默认约束
    final BoxConstraints effectiveConstraints =
        constraints ?? const BoxConstraints(minWidth: 100, maxWidth: 300);

    // 计算边界并调整位置
    final Size screenSize = MediaQuery.of(context).size;
    final double popupWidth = effectiveConstraints.maxWidth.isFinite
        ? effectiveConstraints.maxWidth
        : 300.0;
    final double popupHeight = effectiveConstraints.maxHeight.isFinite
        ? effectiveConstraints.maxHeight
        : 300.0;

    // 调整位置，确保在屏幕内显示
    Offset adjustedPosition = position;

    // 检查右侧边界
    if (position.dx + popupWidth > screenSize.width) {
      adjustedPosition = Offset(
        screenSize.width - popupWidth,
        adjustedPosition.dy,
      );
    }

    // 检查左侧边界
    if (adjustedPosition.dx < 0) {
      adjustedPosition = Offset(0, adjustedPosition.dy);
    }

    // 检查底部边界
    if (position.dy + popupHeight > screenSize.height) {
      adjustedPosition = Offset(
        adjustedPosition.dx,
        screenSize.height - popupHeight,
      );
    }

    // 检查顶部边界
    if (adjustedPosition.dy < 0) {
      adjustedPosition = Offset(adjustedPosition.dx, 0);
    }

    entry = OverlayEntry(
      builder: (context) {
        // 确保背景色非空
        final Color bgColor = _resolveBackgroundColor(context, backgroundColor);
        // 获取边框颜色
        final Color? borderColor2 = _resolveBorderColor(context, borderColor);

        return TapRegion(
          onTapUpOutside: (event) {
            Timer(const Duration(milliseconds: 0), close);
          },
          child: Stack(
            children: [
              Positioned(
                left: adjustedPosition.dx,
                top: adjustedPosition.dy,
                child: _PopupContent(
                  content: builder(context, close),
                  backgroundColor: bgColor,
                  borderColor: borderColor2,
                  borderWidth: borderWidth,
                  elevation: elevation,
                  borderRadius: borderRadius ?? BorderRadius.circular(6),
                  constraints: effectiveConstraints,
                  animationDuration: animationDuration,
                ),
              ),
            ],
          ),
        );
      },
    );

    overlay.insert(entry);
    return entry;
  }

  /// 获取背景色，如果未提供则使用主题背景色
  static Color _resolveBackgroundColor(
    BuildContext context,
    Color? providedColor,
  ) {
    return providedColor ?? context.backgroundWhite;
  }

  /// 获取边框颜色，如果未提供则使用主题边框颜色
  static Color? _resolveBorderColor(
    BuildContext context,
    Color? providedColor,
  ) {
    return providedColor ?? context.border300;
  }

  @override
  State<PopupOverlay> createState() => _PopupOverlayState();
}

/// 浮层控制器
class PopupOverlayController {
  _PopupOverlayState? _state;

  /// 浮层是否可见
  bool get isVisible => _state?._isOverlayVisible ?? false;

  /// 显示浮层
  void show() => _state?._showOverlay();

  /// 隐藏浮层
  void hide() => _state?._closeOverlay();

  /// 切换浮层显示状态
  void toggle() => _state?._toggleOverlay();

  /// 控制器销毁
  void dispose() => _state = null;

  void _attach(_PopupOverlayState state) => _state = state;

  void _detach() => _state = null;
}

/// 浮层内容组件
///
/// 用于渲染浮层内容，处理动画和样式。
/// 当内容超出 constraints.maxHeight 时，将启用滚动功能。
/// 如果未指定 maxHeight，则默认高度为 300，超出部分可滚动。
class _PopupContent extends StatefulWidget {
  final Widget content;
  final Color backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final double elevation;
  final BorderRadius borderRadius;
  final BoxConstraints constraints;
  final Duration animationDuration;

  const _PopupContent({
    Key? key,
    required this.content,
    required this.backgroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    required this.elevation,
    required this.borderRadius,
    required this.constraints,
    required this.animationDuration,
  }) : super(key: key);

  @override
  State<_PopupContent> createState() => _PopupContentState();
}

class _PopupContentState extends State<_PopupContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(opacity: _animation.value, child: child);
      },
      child: Material(
        color: widget.backgroundColor,
        elevation: widget.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: widget.borderRadius,
          side: widget.borderColor != null
              ? BorderSide(
                  color: widget.borderColor!,
                  width: widget.borderWidth,
                )
              : BorderSide.none,
        ),
        child: _buildScrollableContent(),
      ),
    );
  }

  /// 构建可滚动内容
  ///
  /// 根据约束条件创建可滚动内容：
  /// 1. 如果设置了 maxHeight，则使用该值作为最大高度限制
  /// 2. 如果未设置 maxHeight，则使用默认值 300作为最大高度限制
  /// 3. 当内容高度超出约束时，启用滚动
  /// 4. 当内容高度小于maxHeight时，高度自适应内容大小，但不小于minHeight
  Widget _buildScrollableContent() {
    // 获取最大高度，如果未设置则使用默认值300
    final double maxHeight = widget.constraints.maxHeight.isFinite
        ? widget.constraints.maxHeight
        : 300.0;

    final BoxConstraints newConstraints = BoxConstraints(
      minWidth: widget.constraints.minWidth,
      maxWidth: widget.constraints.maxWidth,
      minHeight: widget.constraints.minHeight,
      maxHeight: maxHeight,
    );

    return ConstrainedBox(
      constraints: newConstraints,
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: IntrinsicWidth(child: widget.content),
      ),
    );
  }
}

class _PopupOverlayState extends State<PopupOverlay> {
  OverlayEntry? _overlayEntry;
  bool _isOverlayVisible = false;
  final LayerLink _layerLink = LayerLink();
  PopupOverlayController? _controller;
  late PopupPosition _effectivePosition;

  /// 获取OverlayState
  late OverlayState? _overlay;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller;
    _controller?._attach(this);
    _effectivePosition = widget.position;
    _overlay = widget.navigatorKey.currentState?.overlay;
    _overlay ??= Overlay.of(context);
  }

  @override
  void didUpdateWidget(PopupOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.controller != oldWidget.controller) {
      oldWidget.controller?._detach();
      _controller = widget.controller;
      _controller?._attach(this);
    }
    if (widget.position != oldWidget.position) {
      _effectivePosition = widget.position;
    }
  }

  /// 计算最佳弹出位置，确保浮层在屏幕内可见
  PopupPosition _calculateBestPosition(
    BuildContext context,
    BoxConstraints popupConstraints,
  ) {
    // 获取屏幕尺寸
    final Size screenSize = MediaQuery.of(context).size;

    // 获取触发组件的位置和尺寸
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size targetSize = renderBox.size;
    final Offset targetPosition = renderBox.localToGlobal(Offset.zero);

    // 计算浮层在各方向上的预估尺寸
    final double popupWidth = popupConstraints.maxWidth.isFinite
        ? popupConstraints.maxWidth
        : 300.0;
    final double popupHeight = popupConstraints.maxHeight.isFinite
        ? popupConstraints.maxHeight
        : 300.0;

    // 定义方向优先级，首先尝试用户指定的方向，然后是其他方向
    final List<PopupPosition> positionPriority = [
      widget.position,
      _getOppositePosition(widget.position),
      ...PopupPosition.values.where(
        (p) =>
            p != widget.position && p != _getOppositePosition(widget.position),
      ),
    ];

    // 检查每个方向是否会导致浮层超出屏幕
    for (final position in positionPriority) {
      switch (position) {
        case PopupPosition.bottom:
          if (targetPosition.dy + targetSize.height + popupHeight <=
              screenSize.height) {
            return PopupPosition.bottom;
          }
          break;
        case PopupPosition.top:
          if (targetPosition.dy - popupHeight >= 0) {
            return PopupPosition.top;
          }
          break;
        case PopupPosition.right:
          if (targetPosition.dx + targetSize.width + popupWidth <=
              screenSize.width) {
            return PopupPosition.right;
          }
          break;
        case PopupPosition.left:
          if (targetPosition.dx - popupWidth >= 0) {
            return PopupPosition.left;
          }
          break;
      }
    }

    // 如果所有方向都不理想，返回原始方向，后续可能需要调整偏移量使其尽可能显示
    return widget.position;
  }

  /// 获取位置的相反方向
  PopupPosition _getOppositePosition(PopupPosition position) {
    switch (position) {
      case PopupPosition.bottom:
        return PopupPosition.top;
      case PopupPosition.top:
        return PopupPosition.bottom;
      case PopupPosition.right:
        return PopupPosition.left;
      case PopupPosition.left:
        return PopupPosition.right;
    }
  }

  @override
  void dispose() {
    _closeOverlay();
    _controller?._detach();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(onTap: _toggleOverlay, child: widget.child),
    );
  }

  /// 切换浮层显示状态
  void _toggleOverlay() {
    _isOverlayVisible ? _closeOverlay() : _showOverlay();
  }

  /// 显示浮层
  void _showOverlay() {
    if (_isOverlayVisible) return;

    final overlay = _overlay;
    if (overlay == null) {
      print('PopupOverlay错误: 无法获取Overlay');
      debugPrint('PopupOverlay错误: 无法获取Overlay');
      return;
    }

    // 计算最佳弹出位置
    final BoxConstraints constraints =
        widget.constraints ??
        const BoxConstraints(minWidth: 100, maxWidth: 300);
    _effectivePosition = _calculateBestPosition(context, constraints);

    _overlayEntry = _createOverlayEntry();
    overlay.insert(_overlayEntry!);
    _isOverlayVisible = true;
  }

  /// 关闭浮层
  void _closeOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isOverlayVisible = false;

      // 触发关闭回调
      widget.onClose?.call();
    }
  }

  /// 根据位置获取偏移量
  Offset _getOffsetByPosition() {
    final Offset baseOffset = widget.offset;
    const double gap = 5.0; // 设置5px的间隔

    switch (_effectivePosition) {
      case PopupPosition.bottom:
        return Offset(baseOffset.dx, baseOffset.dy + gap);
      case PopupPosition.top:
        return Offset(baseOffset.dx, baseOffset.dy - gap);
      case PopupPosition.right:
        return Offset(baseOffset.dx + gap, baseOffset.dy);
      case PopupPosition.left:
        return Offset(baseOffset.dx - gap, baseOffset.dy);
    }
  }

  /// 创建浮层Entry
  OverlayEntry _createOverlayEntry() {
    return OverlayEntry(
      builder: (context) {
        // 确保背景色非空
        final Color bgColor = PopupOverlay._resolveBackgroundColor(
          context,
          widget.backgroundColor,
        );
        // 获取边框颜色
        final Color? borderColor = PopupOverlay._resolveBorderColor(
          context,
          widget.borderColor,
        );
        // 获取带间隔的偏移量
        final Offset offsetWithGap = _getOffsetByPosition();

        return TapRegion(
          onTapUpOutside: widget.closeOnTapOutside
              ? (event) {
                  // 必须加延迟否则会跟打开时机冲突
                  Timer(Duration(milliseconds: 0), _closeOverlay);
                }
              : null,
          child: Stack(
            children: [
              CompositedTransformFollower(
                link: _layerLink,
                offset: offsetWithGap,
                followerAnchor: _getFollowerAnchor(),
                targetAnchor: _getTargetAnchor(),
                child: _PopupContent(
                  content: widget.builder(context, _closeOverlay),
                  backgroundColor: bgColor,
                  borderColor: borderColor,
                  borderWidth: widget.borderWidth,
                  elevation: widget.elevation,
                  borderRadius: widget.borderRadius,
                  constraints:
                      widget.constraints ??
                      const BoxConstraints(minWidth: 100, maxWidth: 300),
                  animationDuration: widget.animationDuration,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 获取浮层锚点
  Alignment _getFollowerAnchor() {
    switch (_effectivePosition) {
      case PopupPosition.bottom:
        return Alignment.topLeft;
      case PopupPosition.top:
        return Alignment.bottomLeft;
      case PopupPosition.right:
        return Alignment.centerLeft;
      case PopupPosition.left:
        return Alignment.centerRight;
    }
  }

  /// 获取目标锚点
  Alignment _getTargetAnchor() {
    switch (_effectivePosition) {
      case PopupPosition.bottom:
        return Alignment.bottomLeft;
      case PopupPosition.top:
        return Alignment.topLeft;
      case PopupPosition.right:
        return Alignment.centerRight;
      case PopupPosition.left:
        return Alignment.centerLeft;
    }
  }
}
