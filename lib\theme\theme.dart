import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';
import 'package:jyt_components_package/theme/text_theme.dart';
import 'package:jyt_components_package/theme/button_theme.dart';
import 'package:jyt_components_package/theme/card_theme.dart';
import 'package:jyt_components_package/theme/input_theme.dart';
import 'package:jyt_components_package/theme/dialog_theme.dart';
import 'package:jyt_components_package/theme/font.dart';
import 'package:jyt_components_package/theme/switch_theme.dart';

/// 应用主题
class AppTheme {
  // 亮色主题
  static ThemeData light() {
    return ThemeData(
      fontFamily: AppFont.platformFont, // 设置默认字体
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.white, // 默认背景色
      primarySwatch: AppColors.createMaterialColor(AppColors.primary),
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: Colors.white,
        error: AppColors.error,
      ),

      // 文本主题
      textTheme: AppTextTheme.light(),

      // 卡片主题
      cardTheme: AppCardTheme.lightCardTheme(),

      // 按钮主题
      elevatedButtonTheme: AppButtonTheme.elevatedButtonTheme(),
      outlinedButtonTheme: AppButtonTheme.outlinedButtonTheme(),
      textButtonTheme: AppButtonTheme.textButtonTheme(),
      floatingActionButtonTheme: AppButtonTheme.floatingActionButtonTheme(),
      iconButtonTheme: AppButtonTheme.iconButtonTheme(),

      // 输入框主题
      inputDecorationTheme: AppInputTheme.lightInputTheme(),

      // 对话框主题
      dialogTheme: AppDialogTheme.lightDialogTheme(),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: AppColors.border200,
        thickness: 1,
        space: 1,
      ),

      // Checkbox主题
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return AppColors.primary.withValues(alpha: 0.4);
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
        side: BorderSide(color: AppColors.border300),
      ),

      // Switch主题
      switchTheme: AppSwitchTheme.lightSwitchTheme(),
    );
  }

  // 暗色主题
  static ThemeData dark() {
    return ThemeData(
      fontFamily: AppFont.platformFont, // 设置默认字体
      brightness: Brightness.dark,
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.backgroundDark200, // 默认背景色
      colorScheme: ColorScheme.dark(
        primary: AppColors.primary,
        secondary: AppColors.accent,
        surface: Colors.black,
        error: AppColors.error,
      ),

      // 文本主题
      textTheme: AppTextTheme.dark(),

      // 卡片主题
      cardTheme: AppCardTheme.darkCardTheme(),

      // 按钮主题
      elevatedButtonTheme: AppButtonTheme.elevatedButtonTheme(),
      outlinedButtonTheme: AppButtonTheme.outlinedButtonTheme(),
      textButtonTheme: AppButtonTheme.textButtonTheme(),
      floatingActionButtonTheme: AppButtonTheme.floatingActionButtonTheme(),
      iconButtonTheme: AppButtonTheme.darkIconButtonTheme(),

      // 输入框主题
      inputDecorationTheme: AppInputTheme.darkInputTheme(),

      // 对话框主题
      dialogTheme: AppDialogTheme.darkDialogTheme(),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: AppColors.borderDark200,
        thickness: 1,
        space: 1,
      ),

      // Checkbox暗色主题
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith<Color>((
          Set<WidgetState> states,
        ) {
          if (states.contains(WidgetState.disabled)) {
            return AppColors.primary.withValues(alpha: 0.4);
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
        side: BorderSide(color: AppColors.borderDark300),
      ),

      // Switch暗色主题
      switchTheme: AppSwitchTheme.darkSwitchTheme(),
    );
  }
}
